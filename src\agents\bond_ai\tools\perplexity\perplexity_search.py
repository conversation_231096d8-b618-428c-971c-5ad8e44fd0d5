"""Table filters management tools."""

from typing import Op<PERSON>, Dict, Any, <PERSON><PERSON>, Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolArg
from langgraph.config import get_stream_writer
from bond_ai.configuration import Configuration

from langgraph.types import Command
from langchain_core.messages import ToolMessage
from langchain_core.tools import InjectedToolCallId


@tool
async def perplexity_search(
    tool_call_id: Annotated[str, InjectedToolCallId],
    config: Annotated[RunnableConfig, InjectedToolArg],
    search_query: str,
) -> Command:
    """Search for SDR specific web results about company and people event and signals."""


    system_prompt = "You are a helpful assistant."




    
    
    
    return Command(update={
                "last_error_message": None,
                "messages": [
                    ToolMessage(
                        content= system_prompt,
                        tool_call_id=tool_call_id,
                    )
                ]
            })
