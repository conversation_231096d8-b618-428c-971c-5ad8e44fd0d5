"""Integration tests for domain category search without mocks.

These tests import the tool module directly from its file path (no package
initializers), and exercise the real vector store against the packaged
`domain_categories.txt` file.
"""

from typing import List
import os
import sys
import importlib.util
import pytest

# Require real dependencies instead of mocks; skip if they aren't installed
pytest.importorskip("langchain_core.tools")
pytest.importorskip("langgraph.config")

# Load the module directly to avoid importing the entire tools package
TESTS_DIR = os.path.dirname(__file__)
print(f"TESTS_DIR: {TESTS_DIR}")
MODULE_PATH = os.path.abspath(os.path.join(
    TESTS_DIR, "..",  "tools", "vector_stores.py"
))

spec = importlib.util.spec_from_file_location("domain_category_search", MODULE_PATH)
module = importlib.util.module_from_spec(spec)
assert spec and spec.loader
# Ensure module is discoverable during dataclass processing
sys.modules[spec.name] = module  # type: ignore[arg-type]
spec.loader.exec_module(module)  # type: ignore[union-attr]

DomainCategoryVectorStore = module.DomainCategoryVectorStore  # type: ignore[attr-defined]


## HOW TO EXECUTE AND DISPLAY THE PRINT STATEMENTS
## pytest src\agents\bond_ai\tests\test_domain_category_search_integration.py -v -s

def test_vector_store_returns_technology_for_tech_queries():
    store = DomainCategoryVectorStore()
    queries: List[str] = [
        "tech companies",
        "technology",
        "software companies",
        "computer industry",
        "saas industry",
        "cloud computing",
        "recruiment companies",
    ]



    for q in queries:
        results = store.query(q, top_k=3)
        assert len(results) == 3
        print(f"Query: {q} -> {[f'{result[0].label} ({result[1]:.3f})' for result in results]}")

