from __future__ import annotations

"""Simple domain category search tool.

This tool loads a list of domain categories from a semicolon separated text
file and performs a lightweight token-based similarity search. Each
line in the file is expected to contain the following columns::

    IndustryId;Label;Hierarchy;Description

It is intended to help the build list agent map free-form user queries such as
"tech domain" to the most relevant predefined categories.

The implementation purposely avoids external dependencies or network calls so
that it can run in the test environment without requiring API keys. Similarity
is computed using Jaccard similarity over simple word-prefix tokens.
"""

from dataclasses import dataclass
from pathlib import Path
from typing import List, Tuple, Set
from collections import Counter
import csv
import re
import numpy as np
import os
import hashlib
import pickle
try:
    from openai import OpenAI  # type: ignore
except Exception:  # pragma: no cover - optional dependency
    OpenAI = None  # type: ignore

from langchain_core.tools import tool
from langgraph.config import get_stream_writer

# Default location for the categories file
DEFAULT_CATEGORIES_FILE = Path(__file__).with_name("domain_categories.txt")



@dataclass
class DomainCategory:
    """Represents a single domain category loaded from the CSV file."""
    industry_id: str
    label: str
    hierarchy: str
    description: str

from dotenv import load_dotenv
load_dotenv()
class DomainCategoryVectorStore:
    """In-memory vector store for domain categories.

    Falls back to a deterministic local bag-of-words embedding when OpenAI
    is unavailable (no key or package missing). Caches embeddings on disk
    keyed by a content hash of the input texts.
    """

    def __init__(self, categories_file: Path | str = DEFAULT_CATEGORIES_FILE, include_hierarchy: bool = True):
        # Load categories...
        path = Path(categories_file)
        if not path.exists():
            raise FileNotFoundError(f"Categories file not found: {path}")

        rows: List[List[str]] = []
        with path.open(newline="", encoding="utf-8") as f:
            reader = csv.reader(f, delimiter=";")
            rows = [row for row in reader if any(field.strip() for field in row)]

        if rows and not rows[0][0].strip().isdigit():
            rows = rows[1:]

        self.categories: List[DomainCategory] = [
            DomainCategory(
                industry_id=row[0].strip(),
                label=row[1].strip() if len(row) > 1 else "",
                hierarchy=row[2].strip() if len(row) > 2 else "",
                description=row[3].strip() if len(row) > 3 else "",
            )
            for row in rows
        ]

        # Build text based on include_hierarchy flag
        if include_hierarchy:
            texts = [f"{c.label} {c.hierarchy} {c.description}" for c in self.categories]
        else:
            texts = [f"{c.label} {c.description}" for c in self.categories]

        # Choose embedding backend
        api_key = os.environ.get("OPENAI_API_KEY")
        self._use_openai = bool(api_key) and (OpenAI is not None)
        self.client = OpenAI(api_key=api_key) if self._use_openai else None

        # Pre-compute embeddings using selected backend
        self.embeddings = self._load_or_create_embeddings(texts)

    # ----------------- Caching helpers -----------------
    def _get_content_hash(self, texts: List[str]) -> str:
        h = hashlib.sha256()
        for t in texts:
            h.update(t.encode("utf-8"))
            h.update(b"\0")
        return h.hexdigest()

    def _load_or_create_embeddings(self, texts: List[str]) -> np.ndarray:
        cache_path = Path("category_embeddings.pkl")
        current_hash = self._get_content_hash(texts)

        if cache_path.exists():
            with open(cache_path, 'rb') as f:
                cached_data = pickle.load(f)
            # Backward compatibility: older cache stored raw ndarray
            if isinstance(cached_data, np.ndarray):
                return cached_data
            # New format: dict with embeddings and content_hash
            stored_hash = cached_data.get('content_hash') if isinstance(cached_data, dict) else None
            if stored_hash == current_hash and 'embeddings' in cached_data:
                return cached_data['embeddings']

        # Rebuild embeddings
        embeddings = self._get_embeddings(texts)

        # Save cache
        with open(cache_path, 'wb') as f:
            pickle.dump({'embeddings': embeddings, 'content_hash': current_hash}, f)
        return embeddings

    def _bag_of_words_embed(self, texts: List[str]) -> np.ndarray:
        # Simple 4-char prefix tokenization with light stopwords
        STOPWORDS = {
            "company","companies","co","inc","corp","corporation","llc","ltd","plc",
            "services","service","provider","providers","industry","industries",
            "business","domain","technical","and","the","of","a","an","group","groups",
            "holding","holdings",
        }
        def tokenize(s: str, k: int = 4):
            return [w[:k] for w in re.findall(r"\w+", s.lower()) if w and w not in STOPWORDS]
        vocab: list[str] = []
        seen = set()
        tokenized = []
        for t in texts:
            toks = tokenize(t)
            tokenized.append(toks)
            for tok in toks:
                if tok not in seen:
                    seen.add(tok)
                    vocab.append(tok)
        idx = {tok: i for i, tok in enumerate(vocab)}
        mat = np.zeros((len(texts), len(vocab)), dtype=float)
        for r, toks in enumerate(tokenized):
            counts = Counter(toks)
            for tok, cnt in counts.items():
                mat[r, idx[tok]] = float(cnt)
        # L2 normalize
        norms = np.linalg.norm(mat, axis=1, keepdims=True)
        norms[norms == 0] = 1.0
        return mat / norms

    def _get_embeddings(self, texts: List[str]) -> np.ndarray:
        """Get embeddings for a list of texts using OpenAI if available, otherwise BoW."""
        if self._use_openai and self.client is not None:
            try:
                response = self.client.embeddings.create(input=texts, model="text-embedding-3-small")
                return np.array([data.embedding for data in response.data])
            except Exception:
                pass  # fall back to local embedding
        return self._bag_of_words_embed(texts)

    def query(self, text: str, top_k: int = 3) -> List[Tuple[DomainCategory, float]]:
        # Expand common abbreviations
        expanded_text = text.lower()
        if "saas" in expanded_text:
            expanded_text = expanded_text.replace("saas", "software as a service")
        if "tech" in expanded_text:
            expanded_text = expanded_text.replace("tech", "technology")

        # Get query embedding and compute similarities
        query_embedding = self._get_embeddings([expanded_text])[0]
        similarities = np.dot(self.embeddings, query_embedding) / (
            np.linalg.norm(self.embeddings, axis=1) * np.linalg.norm(query_embedding)
        )

        top_indices = np.argsort(similarities)[::-1][:top_k]
        return [(self.categories[i], float(similarities[i])) for i in top_indices]

@tool
def search_domain_categories(query: str, top_k: int = 3) -> List[str]:
    """Return the most relevant domain categories for the provided query.

    Args:
        query: Free-form user description of a company or people domain.
        top_k: Number of categories to return (default: 3).

    Returns:
        List of the top matching categories ordered from most to least similar.
    """

    stream_writer = get_stream_writer()
    stream_writer({"custom_tool_call": f"Searching domain categories for {query}"})

    store = DomainCategoryVectorStore()
    results = store.query(query, top_k=top_k)
    return [cat.label for cat, _ in results]
