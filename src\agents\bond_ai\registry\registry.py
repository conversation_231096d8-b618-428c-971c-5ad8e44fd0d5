
# ============================================================================
# SIMPLE AGENT REGISTRY
# ============================================================================


from enum import Enum
from dataclasses import dataclass
from typing import List, Any, Optional, Required


#splitted by agent for review and identification
from bond_ai.tools.build_list import build_people_list_from_linkedin
from bond_ai.tools import (
    upsert_linkedin_person_profile_column_from_url,
    upsert_linkedin_company_profile_column_from_url,
    upsert_work_email_column,
    upsert_phone_number_column,
    read_table_data,
    run_column,
    upsert_text_column,
    upsert_ai_text_column,
    upsert_bond_ai_researcher_column,
    upsert_ai_message_copywriter,
    read_user_view_table_filters,
    update_user_view_table_filters_tool
)

# this will cause a circular dependency
#from graphsdk import inject_hitl_wrapper


from bond_ai.prompts_v1 import (
    BUILD_LIST_AGENT_PROMPT,
    LINKEDIN_ENRICHMENT_AGENT_PROMPT,
    EMAIL_PHONE_ENRICHMENT_AGENT_PROMPT,
    TABLE_ACTION_AGENT_PROMPT,
    RUN_COLUMN_CELL_AGENT_PROMPT
)
from src.agents.bond_ai.tools.perplexity.perplexity_search import perplexity_search
from src.agents.bond_ai.tools.vector_stores import search_domain_categories

@dataclass
class AgentInfo:
    """Agent information containing name, description, and tools."""
    name: str
    description: str
    tools: Optional[List[Any]] = None # type: List[Any]
    system_prompt: Optional[str] = None
    prompt_injections: Optional[dict] = None

class SupervisorSubAgents(Enum):
    """
    Enumeration of specialized agents in the Supervisor Agentic Pattern.

    Each agent represents a specific domain of expertise within the outbound sales workflow.
    Agents are designed to work independently while being orchestrated by the supervisor.

    Each enum value contains the agent name, description, and associated tools.
    """

    build_list_agent = AgentInfo(
        name="build_list_agent",
        description="""<primary_role>Uses LinkedIn Sales Navigator to build targeted prospect lists based on user-specified criteria and search parameters</primary_role>
                        
                        <inputs_required> [Required inputs:
                            - User query (string): Natural language prospect criteria (titles, companies, locations, experience, industries)
                            - List mode (enum): "preview" for samples or "full_build" for complete list
                            - List size (integer): Prospects to fetch (1-50,000)] 
                        </inputs_required>
                        
                        <outputs_produced> [Goal accomplishment:
                            - Creates prospect list in Outbond table with populated rows
                            - Provides user confirmation with summary statistics
                            - Returns status to supervisor when done] 
                        </outputs_produced>
                        
                        <specialized_capabilities> [Advanced prospecting:
                            - LinkedIn Sales Navigator API with multi-criteria filtering
                            - Real-time data extraction and job change detection
                            - Boolean search logic and geographic targeting] 
                        </specialized_capabilities>
                        
                        <limitations> [Key constraints: - Only creates new prospect rows, cannot enrich existing lists - Cannot manage table columns or perform individual lookups - LinkedIn-only platform, no outreach execution] </limitations>
                        
                        <example_use_cases> [Prospecting scenarios:
                            - New Market Entry: "VP of Sales at SaaS companies (50-200 employees) in California, recent role changes"
                            - Competitor Analysis: "Marketing Directors at Salesforce competitors in major US tech hubs"
                            - Event-Based: "CTOs at Series B fintech startups with banking background"
                            - Geographic: "Engineering Managers at 100+ employee companies in Austin"
                            - Career Transition: "Consulting-to-tech Business Development roles at $1B+ companies"] 
                        </example_use_cases>
                        <interaction_style> [Communication approach:
                            - Progress updates with completion indicators
                            - Clear error explanations with alternatives
                            - Professional tone with LinkedIn constraint guidance]
                        </interaction_style>
                        <dependencies> [Workflow relationships: - **Upstream**: None - operates from user query independently - **Downstream**: Enables linkedin_enrichment_agent, email_phone_agent, messages_copywriter_agent - **Integration**: Populates Outbond table for immediate downstream access] </dependencies> """,  
        tools=[build_people_list_from_linkedin,search_domain_categories,perplexity_search],
        system_prompt=BUILD_LIST_AGENT_PROMPT
    )

    linkedin_enrichment_agent = AgentInfo(
       name="linkedin_enrichment_agent",
        description="""<primary_role>Creates LinkedIn profile columns by scraping detailed public data from LinkedIn people and company profiles</primary_role>
                        
                        <inputs_required> [Required inputs:
                            - LinkedIn URLs from table: Either linkedin_profile_url (people) or linkedin_company_url (company)
                            - Column type request: Specify "people_profile" or "company_profile" column creation] 
                        </inputs_required>
                        
                        <outputs_produced> [Goal accomplishment:
                            - Creates LinkedIn profile columns in Outbond table, already enriched with 5 rows for user preview
                            - Populates 5 rows in the columns with comprehensive profile data (experience, company details, descriptions)
                            - Provides user confirmation of enrichment completion
                            - Returns status to supervisor] 
                        </outputs_produced>
                        
                        <specialized_capabilities> [LinkedIn data extraction:
                            - Real-time scraping of public LinkedIn people and company profiles
                            - Comprehensive data extraction (full names, job history, company websites, descriptions)
                            - Automatic company profile derivation from people profile URLs
                            - Bulk processing with rate limiting and anti-detection measures] 
                        </specialized_capabilities>
                        
                        <limitations> [Key constraints:
                            - Only creates LinkedIn profile columns, no other column types
                            - Requires existing LinkedIn URLs in table to function
                            - Cannot access private or restricted LinkedIn profiles
                            - Cannot perform LinkedIn searches or URL discovery] 
                        </limitations>
                        
                        <example_use_cases> [Enrichment scenarios:
                            - Profile Enrichment: "Enrich the LinkedIn profiles of the list" → Creates people profile columns with full job history
                            - Company Data: "Create a LinkedIn company column" → Extracts company descriptions, websites, employee counts
                            - Dual Enrichment: User has people URLs → Creates both people and company columns automatically
                            - Post-List Building: After build_list_agent creates prospect list → Enriches existing LinkedIn URLs with detailed data
                            - Preparation for Outreach: Before email finding → Provides full names and company websites for email_phone_agent] 
                        </example_use_cases>
                        
                        <interaction_style> [Communication approach:
                            - Progress updates during bulk profile scraping
                            - Clear confirmation of columns created with data summary] 
                        </interaction_style>
                        
                        <dependencies> [Workflow relationships:
                            - **Upstream**: Requires build_list_agent for initial URLs, or perplexity_agent for URL discovery, or existing URLs in the table
                            - **Downstream**: Enables email_phone_agent with names/websites, messages_copywriter_agent with profile insights
                            - **Integration**: Creates structured columns for immediate downstream agent utilization] 
                        </dependencies> """,

        tools=[
            upsert_linkedin_person_profile_column_from_url,
            upsert_linkedin_company_profile_column_from_url
        ],
        system_prompt=LINKEDIN_ENRICHMENT_AGENT_PROMPT,
        prompt_injections={"table_summary": "table_summary"}
    )

    email_phone_agent = AgentInfo(
        name="email_phone_agent",
        description="""<primary_role>Creates work email and phone number columns by fetching verified contact information based on prospect data</primary_role>
                        
                        <inputs_required> [Required inputs:
                            - For email column: Full name and company domain from table
                            - For phone column: LinkedIn profile URL from table] 
                        </inputs_required>
                        
                        <outputs_produced> [Goal accomplishment:
                            - Creates work email column with verified email addresses
                            - Creates phone number column with validated phone numbers
                            - Provides user confirmation of enrichment completion with data summary
                            - Returns status to supervisor] 
                        </outputs_produced>
                        
                        <specialized_capabilities> [Contact data enrichment:
                            - Real-time fetching of verified work email addresses
                            - Waterfall enrichment across multiple providers for maximum coverage
                            - Email validation and deliverability verification
                            - Phone number extraction and validation] 
                        </specialized_capabilities>
                        
                        <limitations> [Key constraints:
                            - Only creates email and phone columns, no other contact data types
                            - Cannot perform individual lookups — processes entire table columns
                            - Requires existing input data (names, domains, URLs) in table
                            - Cannot generate contact information without proper source data] 
                        </limitations>
                        
                        <example_use_cases> [Contact enrichment scenarios:
                            - Email Enrichment: "Enrich the work email of list in my table" → Creates email column using names and domains
                            - Phone Enrichment: "Enrich the phone number" → Creates phone column from LinkedIn URLs
                            - Complete Contact: "Enrich the contact info" → Creates both email and phone columns
                            - Pre-Outreach: "Create a work email column" → Prepares verified emails for campaign execution
                            - Multi-Channel Setup: After other enrichment → Adds contact methods for comprehensive outreach] 
                        </example_use_cases>
                        
                        <interaction_style> [Communication approach:
                            - Progress updates during column creation and data fetching
                            - Clear confirmation of columns created with validation summary] 
                        </interaction_style>
                        
                        <dependencies> [Workflow relationships:
                            - **Upstream**: Requires linkedin_enrichment_agent for names/domains, build_list_agent for URLs, or perplexity_research_agent for missing data
                            - **Downstream**: Enables integration agents for outreach tool connectivity and campaign execution
                            - **Integration**: Creates verified contact columns for immediate outreach utilization] 
                        </dependencies> """,

        tools=[
            upsert_phone_number_column,
            upsert_work_email_column
        ],

        system_prompt=EMAIL_PHONE_ENRICHMENT_AGENT_PROMPT,
        prompt_injections={"table_summary": "table_summary"}
    )



    table_action_agent = AgentInfo(
        name="table_action_agent",
        description="<primary_role>Table data operations and filter management<primary_role",
        tools=[
            read_table_data,
            read_user_view_table_filters,
            update_user_view_table_filters_tool,
            upsert_text_column,
            upsert_ai_text_column,
            upsert_bond_ai_researcher_column,
            upsert_ai_message_copywriter
        ],
        system_prompt=TABLE_ACTION_AGENT_PROMPT
    )

    run_column_cell_agent = AgentInfo(
        name="run_column_cell_agent",
        description="<primary_role>Used for enrichment columns with is_runnable=true. Run the entire column or a specific cell.<primary_role",
        tools=[run_column],
        system_prompt=RUN_COLUMN_CELL_AGENT_PROMPT
    )
    #custom agent nodes
    icp_and_persona_agent = AgentInfo(
        name="icp_and_persona_agent",
        description="<primary_role>Creates comprehensive ICP and personas with proper completion handling<primary_role",

    )
    planner_agent = AgentInfo(
        name="planner_agent",
        description="<primary_role>Creates comprehensive plans for complex tasks<primary_role"
    )



    # Future/deprecated agents
    #  agent_message_copy = "agent_message_copy"
    #   agent_action_table = "agent_action_table"
    #   agent_upload_csv = "agent_upload_csv"
    #   agent_http_column = "agent_http_column"
    #   agent_webhook_column = "agent_webhook_column"
    #   agent_formula = "agent_formula"
    #   agent_clean_up_column = "agent_clean_up_column"


class SupervisorSubagentRegistry:
    """Simple registry for managing agent configurations."""

    def __init__(self):
        self.agents = {}
        # self.tools = {}
        self.nodes = {}  # Store pre-built node functions

    def register_react_prebuild_agent(self, name: str, system_prompt: str, tools: list, description: str, enabled: bool = True, prompt_injections: dict = None, **kwargs):
        """Register a react prebuild agent with optional state injection."""
        self.agents[name] = {
            "system_prompt": system_prompt,
            "tools": tools,
            "enabled": enabled,
            "description": description,
            "type": "react_agent",
            "prompt_injections": prompt_injections or {},
            **kwargs
        }
        return self

    def register_custom_agent(self, name: str, node_function, description: str, enabled: bool = True, **kwargs):
        """Register a custom agent."""
        self.agents[name] = {
            #"system prompt"
            "node_function": node_function,
            #"tools"
            "enabled": enabled,
            "description": description,
            "type": "custom_node",

            **kwargs
        }
        return self

    # def register_tool(self, name: str, tool):
    #     """Register a tool by name."""
    #     self.tools[name] = tool
    #     return self

    ## SUPPORT FUNCTION - TO EVALUATE what is required
    def get_enabled_agents(self):
        """Get all enabled agent configurations."""
        return {name: config for name, config in self.agents.items() if config.get("enabled", True)}

    def get_agent_names(self):
        """Get list of enabled agent names."""
        return list(self.get_enabled_agents().keys())

    def get_react_agents(self):
        """Get only ReAct agents (not custom nodes)."""
        return {name: config for name, config in self.get_enabled_agents().items()
                if config.get("type") == "react_agent"}

    def get_custom_nodes(self):
        """Get only custom node functions."""
        return {name: config for name, config in self.get_enabled_agents().items()
                if config.get("type") == "custom_node"}

    def enable_agent(self, name: str):
        """Enable an agent."""
        if name in self.agents:
            self.agents[name]["enabled"] = True

    def disable_agent(self, name: str):
        """Disable an agent."""
        if name in self.agents:
            self.agents[name]["enabled"] = False

# Create global registry
supervisor_sub_agent_registry = SupervisorSubagentRegistry()

## PROMPT UTILITY


def build_workers_directory_xml() -> str:
    """Build an XML agent directory WITHOUT listing tools for each agent.

    This mirrors build_workers_directory_with_tools_xml but omits the <tools> section.
    """
    lines = ["<agent_directory>"]
    from html import escape,unescape

    # ReAct agents (no tools listed)
    for name, config in sorted(supervisor_sub_agent_registry.get_react_agents().items()):
        desc = config.get("description", "ReAct agent")
        lines.append(f'  <agent>')
        lines.append(f"    <name>{escape(name)}</name>")
        lines.append(f"    {desc}")
        lines.append("  </agent>")

    # Custom nodes
    for name, config in sorted(supervisor_sub_agent_registry.get_custom_nodes().items()):
        desc = config.get("description", "")
        lines.append(f'  <agent>')
        lines.append(f"    <name>{escape(name)}</name>")
        lines.append(f"    {desc}")
        lines.append("  </agent>")

    lines.append("</agent_directory>")
    return "\n".join(lines)



